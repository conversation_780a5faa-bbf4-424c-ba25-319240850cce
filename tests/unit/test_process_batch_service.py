import pytest
import uuid
from unittest.mock import <PERSON><PERSON><PERSON>, patch, AsyncMock

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from services import N1ProcessPipeline
from models import UserORM, UserRecordRequestORM

@pytest.mark.unit
class TestProcessBatchService:
    
    @pytest.fixture
    def mock_connection_manager(self):
        """Create a mock ConnectionManager for testing."""
        from ws_service import ConnectionManager
        from unittest.mock import AsyncMock, MagicMock
        mock_manager = MagicMock(spec=ConnectionManager)
        mock_manager.broadcast_update = AsyncMock()
        return mock_manager

    @pytest.fixture
    def pipeline(self, mock_storage_service, db_session, mock_connection_manager):
        """Create an N1ProcessPipeline instance with mocked storage service and patched db session."""
        with patch("services.FileStorageService", return_value=mock_storage_service), \
             patch("services.get_db_session", return_value=db_session):
            pipeline = N1ProcessPipeline(mock_connection_manager)
            pipeline.storage_service = mock_storage_service
            yield pipeline
    
    @pytest.fixture
    def mock_user(self, db_session):
        """Create a mock user in the database."""
        user_id = uuid.uuid4()
        user = UserORM(
            id=user_id,
            bubble_id="12345678",
            email="<EMAIL>",
            develop_mode=False,
            bucket_name="test-bucket"
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        return user
    
    @pytest.fixture
    def mock_records(self, db_session, mock_user):
        """Create mock records in the database for a batch."""
        batch_id = f"batch-{str(uuid.uuid4())[:8]}"
        records = []
        
        # Create 3 records for the batch
        for i in range(3):
            record_id = str(uuid.uuid4())
            record = UserRecordRequestORM(
                id=record_id,
                user_id=str(mock_user.id),
                file_name=f"test{i}.pdf",
                type="RECORD",
                status="PENDING",
                progress=0,
                batch_id=batch_id
            )
            db_session.add(record)
            records.append(record)
        
        db_session.commit()
        for record in records:
            db_session.refresh(record)
        
        return {"batch_id": batch_id, "records": records}
    
    @pytest.mark.asyncio
    async def test_update_batch_user_records(self, pipeline, mock_user, mock_records, monkeypatch):
        """Test updating batch user records."""
        # Mock the __process_batch_update method
        process_batch_mock = AsyncMock()
        monkeypatch.setattr(pipeline, "_N1ProcessPipeline__process_batch_update", process_batch_mock)
        
        # Setup test parameters
        batch_id = mock_records["batch_id"]
        parser_type = "Sequential"
        parser_cloud = "Google"
        background_tasks = MagicMock()
        
        # Call the method
        result = await pipeline.update_batch_user_records(
            str(mock_user.id),
            batch_id,
            parser_type,
            parser_cloud,
            background_tasks,
            None  # parser_model
        )
        
        # Verify the result
        assert result["batch_id"] == batch_id
        assert result["status"] == "queued"
        assert "updated_at" in result
        assert result["record_count"] == 3
        
        # Verify the background task was added
        background_tasks.add_task.assert_called_once()
        args, _ = background_tasks.add_task.call_args
        assert args[0] == pipeline._N1ProcessPipeline__process_batch_update
        assert args[1] == str(mock_user.id)
        assert args[2] == mock_user.bucket_name
        assert args[3] == parser_type
        assert args[4] == parser_cloud
        assert len(args[5]) == 3  # Should be a list of 3 record IDs
        
        # Verify all record IDs are included
        record_ids = [record.id for record in mock_records["records"]]
        for record_id in record_ids:
            assert record_id in args[5]

        # Verify parser_model parameter is included
        assert args[6] == mock_records["batch_id"]  # batch_id
        assert args[7] is None  # parser_model
    
    @pytest.mark.asyncio
    async def test_update_batch_user_records_no_records(self, pipeline, mock_user, monkeypatch):
        """Test updating batch user records with no records found."""
        # Setup test parameters
        batch_id = f"nonexistent-batch-{str(uuid.uuid4())[:8]}"
        parser_type = "Sequential"
        parser_cloud = "Google"
        background_tasks = MagicMock()
        
        # Call the method
        result = await pipeline.update_batch_user_records(
            str(mock_user.id),
            batch_id,
            parser_type,
            parser_cloud,
            background_tasks,
            None  # parser_model
        )
        
        # Verify the result
        assert result["batch_id"] == batch_id
        assert result["status"] == "queued"
        assert "updated_at" in result
        assert result["record_count"] == 0
        
        # Verify the background task was added with an empty list of record IDs
        background_tasks.add_task.assert_called_once()
        args, _ = background_tasks.add_task.call_args
        assert args[0] == pipeline._N1ProcessPipeline__process_batch_update
        assert args[1] == str(mock_user.id)
        assert args[2] == mock_user.bucket_name
        assert args[3] == parser_type
        assert args[4] == parser_cloud
        assert len(args[5]) == 0  # Should be an empty list
        assert args[6] == batch_id  # batch_id
        assert args[7] is None  # parser_model
    
    @pytest.mark.asyncio
    async def test_update_batch_user_records_error(self, pipeline, mock_user):
        """Test updating batch user records with an error."""
        # Setup test parameters
        batch_id = f"batch-{str(uuid.uuid4())[:8]}"
        parser_type = "Sequential"
        parser_cloud = "Google"
        background_tasks = MagicMock()
        
        # Mock the get_user_details method to raise an exception
        with patch.object(pipeline, 'get_user_details', side_effect=Exception("Test error")):
            # Verify that HTTPException is raised
            with pytest.raises(HTTPException) as excinfo:
                await pipeline.update_batch_user_records(
                    str(mock_user.id),
                    batch_id,
                    parser_type,
                    parser_cloud,
                    background_tasks,
                    None  # parser_model
                )
            
            assert excinfo.value.status_code == 500
            # The actual error message is just "Test error", so check for that
            assert "Test error" in excinfo.value.detail
